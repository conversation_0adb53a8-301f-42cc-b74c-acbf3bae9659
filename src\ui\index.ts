// index.ts
// 负责导入和组装各个子模块

import { UIElements } from "./ui-elements";
import { UICreators } from "./ui-creators";
import { UIHandlers } from "./ui-handlers";
import { UIUpdaters } from "./ui-updaters";

export class UI {
    static applyStyles = UICreators.applyStyles;
    static updateKeyDisplay = UIUpdaters.updateKeyDisplay;
    static createButton = UICreators.createButton;
    static createApiKeyTypeSelect = UICreators.createApiKeyTypeSelect;
    static createPanelTitle = UICreators.createPanelTitle;
    static createPanelToggleButton = UICreators.createPanelToggleButton;
    static createKeyDisplayArea = UICreators.createKeyDisplayArea;
    static createApiKeySelectArea = UICreators.createApiKeySelectArea;
    static createActionsArea = UICreators.createActionsArea;
    static createProgressArea = UICreators.createProgressArea;
    static createStatusArea = UICreators.createStatusArea;
    static toggleControlPanel = (): void => {
        UIElements.controlPanelVisible = !UIElements.controlPanelVisible;
        if (UIElements.controlPanelElement) {
            UIElements.controlPanelElement.style.display = UIElements.controlPanelVisible
                ? "flex"
                : "none";
            if (UIElements.controlPanelVisible) {
                UIUpdaters.updateKeyDisplay(); // Refresh display when shown
                // Apply current panel content visibility state
                UIUpdaters.updatePanelContentVisibility();
            }
        }
    }
    static handleTogglePanelContent = UIHandlers.handleTogglePanelContent;
    static handleRunSearch = UIHandlers.handleRunSearch;
    static handleCopyKeys = UIHandlers.handleCopyKeys;
    static handleClearKeys = UIHandlers.handleClearKeys;
    static handleCopyDebugInfo = UIHandlers.handleCopyDebugInfo;
    static updateProgressDisplay = UIUpdaters.updateProgressDisplay;

    /**
     * Creates the main control panel UI.
     * Orchestrates the creation of all panel sub-elements.
     * @returns {HTMLDivElement} The main panel element.
     */
    static createControlPanel = (): HTMLDivElement => {
        const panel: HTMLDivElement = document.createElement("div");
        panel.id = "apiKeyFinderPanel";

        panel.appendChild(UICreators.createPanelTitle());
        panel.appendChild(UICreators.createPanelToggleButton());
        panel.appendChild(UICreators.createKeyDisplayArea());
        panel.appendChild(UICreators.createApiKeySelectArea());
        panel.appendChild(UICreators.createActionsArea());
        panel.appendChild(UICreators.createProgressArea());
        panel.appendChild(UICreators.createStatusArea());

        // Initial visibility will be set in initialize() and updatePanelContentVisibility

        return panel;
    }
}
export default UI;
