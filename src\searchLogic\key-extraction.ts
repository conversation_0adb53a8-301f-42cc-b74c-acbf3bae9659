import { API_KEY_PATTERNS } from "../constants";
import type { ApiServiceName, IKeyExtractor } from "../types";

/**
 * Extracts API keys from text content using pattern matching
 */
export class ApiKeyExtractor implements IKeyExtractor {
    /**
     * Extracts potential API keys from text content based on service-specific patterns.
     */
    public extractPotentialKeys(textContent: string, serviceName: ApiServiceName): string[] {
        const patterns = API_KEY_PATTERNS[serviceName];
        if (!patterns) {
            return [];
        }

        const foundKeys = new Set<string>();

        patterns.forEach((pattern: RegExp) => {
            const currentRegex = new RegExp(pattern.source, pattern.flags);
            let match: RegExpExecArray | null;

            while ((match = currentRegex.exec(textContent)) !== null) {
                foundKeys.add(match[0]);
            }
        });

        return Array.from(foundKeys);
    }
}

export default ApiKeyExtractor;