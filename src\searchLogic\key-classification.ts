import { GM_getValue } from '$';
import { AI_SERVICES, API_KEY_PATTERNS } from "../constants";

class KeyClassifier {
    /**
     * Classifies a single API key based on known patterns, including specific AI services.
     * @param {string} key - The API key string.
     * @returns {string} The category of the key (e.g., "OpenAI", "Gemini", "Grok", "Claude").
     */
    public classifyKey(key: string): string {
        if (typeof key !== "string") return "Other";

        for (const service of AI_SERVICES) {
            const patterns = API_KEY_PATTERNS[service.name as keyof typeof API_KEY_PATTERNS];
            if (patterns) {
                for (const regex of patterns) {
                    const testRegex = new RegExp(regex.source, regex.flags); // Ensure fresh regex for stateful flags like 'g'
                    if (testRegex.test(key)) {
                        return service.name; // This is the specific AI service name
                    }
                }
            }
        }
        return "Other";
    }

    /**
     * Retrieves all stored API keys, categorized including specific AI services.
     * @returns {Object<string, string[]>} An object where keys are category names
     *                                     (e.g., "GitHub", "AWS", "OpenAI", "Other")
     *                                     and values are arrays of key strings.
     */
    public getStoredCategorizedKeys(): { [key: string]: string[] } {
        const categories = AI_SERVICES.map((service) => service.name); // Only include AI service categories

        const categorizedKeys: { [key: string]: string[] } = {};
        categories.forEach((category: string) => {
            // For AI services like OpenAI, Claude, etc.
            // Stored as, e.g., openaiApiKeys, geminiApiKeys
            const storageKey = `${category.charAt(0).toLowerCase()}${category.slice(
                1
            )}ApiKeys`;
            categorizedKeys[category] = GM_getValue(storageKey, []);
        });

        return categorizedKeys;
    }
}

export default KeyClassifier;