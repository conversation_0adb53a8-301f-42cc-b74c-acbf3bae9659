

// --- CSS Styles ---
const PANEL_STYLES = `
      #apiKeyFinderPanel {
        position: fixed;
        bottom: 10px;
        right: 10px;
        width: 380px;
        max-height: 500px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 14px;
        /* Initial display handled by JS */
        flex-direction: column;
        padding: 12px;
        transition: all 0.3s ease;
      }

      #apiKeyFinderPanel:hover {
        box-shadow: 0 6px 16px rgba(0,0,0,0.2);
      }

      .toggle-panel-button {
        background-color: #555; /* Dark gray */
        color: #fff; /* White text */
        border: none;
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .toggle-panel-button:hover {
        background-color: #333; /* Darker gray on hover */
        transform: translateY(-1px);
      }

      .toggle-panel-button:active {
        transform: translateY(0);
      }


      #apiKeyFinderPanel h3 {
        margin: 0 0 12px 0;
        font-size: 18px;
        color: #212529;
        text-align: center;
        font-weight: 600;
      }

      #apiKeyDisplayArea {
        flex-grow: 1;
        overflow-y: auto;
        border: 1px solid #e9ecef;
        padding: 10px;
        margin-bottom: 12px;
        background-color: #fff;
        min-height: 100px;
        border-radius: 6px;
        white-space: pre-wrap;
        word-break: break-word;
        transition: all 0.2s ease;
      }

      #apiKeyDisplayArea:hover {
        border-color: #ced4da;
      }


      #apiKeyDisplayArea div {
        padding: 4px 0;
        border-bottom: 1px solid #f1f3f5;
        transition: background-color 0.2s;
      }

      #apiKeyDisplayArea div:hover {
        background-color: #f8f9fa;
      }

      #apiKeyDisplayArea div:last-child {
        border-bottom: none;
      }

      #apiKeyFinderPanel .actions {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
      }

      #apiKeyFinderPanel .actions button {
        background-color: #0d6efd;
        color: white;
        border: none;
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      #apiKeyFinderPanel .actions button:hover {
        background-color: #0b5ed7;
        transform: translateY(-1px);
      }

      #apiKeyFinderPanel .actions button:active {
        transform: translateY(0);
      }

      #apiKeyFinderPanel .actions button.clear {
        background-color: #dc3545;
      }

      #apiKeyFinderPanel .actions button.clear:hover {
        background-color: #bb2d3b;
      }

      #apiKeyFinderStatus, #apiKeyFinderProgress {
        font-size: 13px;
        color: #495057;
        margin-top: 8px;
        text-align: center;
        min-height: 20px;
      }

      #apiKeyFinderProgress {
        color: #0d6efd;
        font-weight: 600;
        margin-bottom: 8px;
      }

      #apiKeyTypeSelect {
        width: 100%;
        padding: 8px;
        border-radius: 6px;
        border: 1px solid #ced4da;
        margin-bottom: 12px;
        transition: border-color 0.2s;
      }

      #apiKeyTypeSelect:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
      }
    `;
export default PANEL_STYLES;