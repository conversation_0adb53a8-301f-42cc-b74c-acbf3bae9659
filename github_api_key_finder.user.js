/**
 * Promisifies GM_xmlhttpRequest.
 * @param {object} options - Options for GM_xmlhttpRequest.
 * @returns {Promise<object>} A promise that resolves with the response or rejects with an error.
 */
function promisifiedRequest(options) {
  return new Promise((resolve, reject) => {
    GM_xmlhttpRequest({
      ...options,
      onload: function (response) {
        resolve(response);
      },
      onerror: function (error) {
        reject(error);
      },
      ontimeout: function (timeout) {
        reject(timeout);
      },
    });
  });
}

// ==UserScript==
// @name         GitHub API Key Finder
// @namespace    http://tampermonkey.net/
// @version      0.0.1
// @description  Automatically search for AI service API keys on GitHub with a UI.
// <AUTHOR>
// @match        *://github.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_registerMenuCommand
// @grant        GM_setClipboard
// ==/UserScript==

(function () {
  "use strict";

  // --- Shared Utility / Debugging ---
  // Stores various debug messages categorized by type.
  const MAX_DEBUG_MESSAGES_PER_CATEGORY = 200; // Limit the number of messages per category
  const debugMessages = {
    general: { title: "通用日志", messages: [] },
    jsonExtract: { title: "提取的 JSON (片段)", messages: null }, // Special case, stores a single string
    rawUrlConstructed: { title: "构造的 Raw URLs", messages: [] },
    fileFetch: { title: "Raw 文件获取状态", messages: [] },
    error: { title: "错误信息", messages: [] },
  };

  /**
   * Logs a debug message to the console and updates the debugMessages object.
   * @param {string|object} message - The message to log.
   * @param {string} type - The type of debug message (e.g., "jsonExtract", "rawUrlConstructed").
   */
  function debugLog(message, type = "general") {
    // Ensure type corresponds to a key in debugMessages
    console.log(`[DEBUG-${type.toUpperCase()}]:`, message); // Keep detailed console output

    const timestamp = new Date().toLocaleTimeString();
    const formattedMessage = `[${timestamp}] ${
      typeof message === "object" ? JSON.stringify(message, null, 2) : message
    }`;

    if (debugMessages[type]) {
      if (type === "jsonExtract") {
        // Handle the special 'jsonExtract' case which stores a single formatted string (or null)
        if (typeof message === "object" && message.data) {
          debugMessages.jsonExtract.messages = `[${timestamp}] ${JSON.stringify(
            message.data,
            null,
            2
          ).substring(0, 1000)}... (截断)`;
        } else {
          // Store the raw message if it's not in the expected object format
          debugMessages.jsonExtract.messages = formattedMessage;
        }
      } else if (Array.isArray(debugMessages[type].messages)) {
        debugMessages[type].messages.push(formattedMessage);
        // Limit the number of messages
        if (
          debugMessages[type].messages.length > MAX_DEBUG_MESSAGES_PER_CATEGORY
        ) {
          debugMessages[type].messages.shift(); // Remove the oldest message
        }
      } else {
        // Fallback for misconfigured types, log to general
        console.warn(
          `[DEBUG] Invalid debug message type configuration for: ${type}. Logging to general.`
        );
        debugMessages.general.messages.push(
          `[${type}-UNHANDLED] ${formattedMessage}`
        );
      }
    } else {
      console.warn(
        `[DEBUG] Unknown debug message type: ${type}. Logging to general.`
      );
      debugMessages.general.messages.push(
        `[${type}-UNKNOWN] ${formattedMessage}`
      );
    }
  }

  // --- CSS Styles ---
  const PANEL_STYLES = `
      #apiKeyFinderPanel {
        position: fixed;
        bottom: 10px;
        right: 10px;
        width: 380px;
        max-height: 500px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 14px;
        /* Initial display handled by JS */
        flex-direction: column;
        padding: 12px;
        transition: all 0.3s ease;
      }

      #apiKeyFinderPanel:hover {
        box-shadow: 0 6px 16px rgba(0,0,0,0.2);
      }

      .toggle-panel-button {
        background-color: #555; /* Dark gray */
        color: #fff; /* White text */
        border: none;
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .toggle-panel-button:hover {
        background-color: #333; /* Darker gray on hover */
        transform: translateY(-1px);
      }

      .toggle-panel-button:active {
        transform: translateY(0);
      }


      #apiKeyFinderPanel h3 {
        margin: 0 0 12px 0;
        font-size: 18px;
        color: #212529;
        text-align: center;
        font-weight: 600;
      }

      #apiKeyDisplayArea {
        flex-grow: 1;
        overflow-y: auto;
        border: 1px solid #e9ecef;
        padding: 10px;
        margin-bottom: 12px;
        background-color: #fff;
        min-height: 100px;
        border-radius: 6px;
        white-space: pre-wrap;
        word-break: break-word;
        transition: all 0.2s ease;
      }

      #apiKeyDisplayArea:hover {
        border-color: #ced4da;
      }


      #apiKeyDisplayArea div {
        padding: 4px 0;
        border-bottom: 1px solid #f1f3f5;
        transition: background-color 0.2s;
      }

      #apiKeyDisplayArea div:hover {
        background-color: #f8f9fa;
      }

      #apiKeyDisplayArea div:last-child {
        border-bottom: none;
      }

      #apiKeyFinderPanel .actions {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
      }

      #apiKeyFinderPanel .actions button {
        background-color: #0d6efd;
        color: white;
        border: none;
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      #apiKeyFinderPanel .actions button:hover {
        background-color: #0b5ed7;
        transform: translateY(-1px);
      }

      #apiKeyFinderPanel .actions button:active {
        transform: translateY(0);
      }

      #apiKeyFinderPanel .actions button.clear {
        background-color: #dc3545;
      }

      #apiKeyFinderPanel .actions button.clear:hover {
        background-color: #bb2d3b;
      }

      #apiKeyFinderStatus, #apiKeyFinderProgress {
        font-size: 13px;
        color: #495057;
        margin-top: 8px;
        text-align: center;
        min-height: 20px;
      }

      #apiKeyFinderProgress {
        color: #0d6efd;
        font-weight: 600;
        margin-bottom: 8px;
      }

      #apiKeyTypeSelect {
        width: 100%;
        padding: 8px;
        border-radius: 6px;
        border: 1px solid #ced4da;
        margin-bottom: 12px;
        transition: border-color 0.2s;
      }

      #apiKeyTypeSelect:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
      }
    `;

  // --- UI Module ---
  const UI = (function () {
    // --- UI State Variables ---
    let controlPanelVisible = true; // Visibility state of the control panel
    let panelContentVisible = true; // Visibility state of the panel's main content

    // --- UI Element References ---
    let controlPanelElement = null;
    let keysDisplayElement = null;
    let statusElement = null;
    let apiKeyTypeSelectElement = null;
    let apiKeyTypeSelectContainer = null;
    let progressElement = null;
    let togglePanelContentButton = null;
    let searchButtonElement = null;

    /**
     * Applies CSS styles for the control panel to the document head.
     */
    function applyStyles() {
      const style = document.createElement("style");
      style.textContent = PANEL_STYLES;
      document.head.appendChild(style);
    }

    /**
     * Appends a section of keys to a parent DOM element.
     * @param {string} title - The title for this section of keys.
     * @param {string[]} keysArray - An array of key strings.
     */

    /**
     * Updates the display area with the total count of stored API keys and newly found keys.
     */
    function updateKeyDisplay() {
      if (!keysDisplayElement) return;

      const categorizedKeys = SearchLogic.getStoredCategorizedKeys(); // Long-term storage
      const newlyFoundKeys = GM_getValue("newlyFoundApiKeys", []); // Newly found in this session

      keysDisplayElement.innerHTML = ""; // Clear previous keys

      let totalLongTermKeyCount = 0;
      const categoryCounts = {};

      // Calculate total long-term count and category counts
      SearchLogic.AI_SERVICES.forEach((service) => {
        const serviceKeys = categorizedKeys[service.name] || [];
        totalLongTermKeyCount += serviceKeys.length;
        categoryCounts[service.name] = serviceKeys.length;
      });

      // Display total long-term count and newly found count
      let displayHtml = `<div>已存储 AI 服务密钥总数: ${totalLongTermKeyCount}</div>`;
      displayHtml += `<div>本次新发现密钥数: ${newlyFoundKeys.length}</div>`;

      keysDisplayElement.innerHTML = displayHtml;

      // Keep the status element update for detailed counts
      if (statusElement) {
        let statusText = `已存储 ${totalLongTermKeyCount} 个 AI 服务密钥 (`;
        const parts = [];
        SearchLogic.AI_SERVICES.forEach((service) => {
          if (categoryCounts[service.name] > 0)
            parts.push(`${service.name}: ${categoryCounts[service.name]}`);
        });
        statusText += parts.join(", ") + ")";
        statusText += ` | 本次新发现: ${newlyFoundKeys.length}`;
        statusElement.textContent = statusText;
      }
    }

    /**
     * Creates and returns a button element.
     * @param {string} text - The button text.
     * @param {function} onClickHandler - The event handler for button click.
     * @param {string} [className] - Optional CSS class name.
     * @returns {HTMLButtonElement} The created button element.
     */
    function createButton(text, onClickHandler, className) {
      const button = document.createElement("button");
      button.textContent = text;
      button.addEventListener("click", onClickHandler);
      if (className) {
        button.className = className;
      }
      return button;
    }

    /**
     * Creates the API type selection dropdown.
     * @returns {HTMLDivElement} The div containing the label and select element.
     */
    function createApiKeyTypeSelect() {
      const selectDiv = document.createElement("div");
      selectDiv.style.marginBottom = "10px";
      selectDiv.style.textAlign = "center";

      const label = document.createElement("label");
      label.textContent = "选择API类型: ";
      label.htmlFor = "apiKeyTypeSelect";
      selectDiv.appendChild(label);

      apiKeyTypeSelectElement = document.createElement("select");
      apiKeyTypeSelectElement.id = "apiKeyTypeSelect";
      apiKeyTypeSelectElement.style.padding = "5px";
      apiKeyTypeSelectElement.style.borderRadius = "3px";

      const allOption = document.createElement("option");
      allOption.value = "ALL";
      allOption.textContent = "全部类型";
      apiKeyTypeSelectElement.appendChild(allOption);

      SearchLogic.AI_SERVICES.forEach((service) => {
        const option = document.createElement("option");
        option.value = service.name;
        option.textContent = service.name;
        apiKeyTypeSelectElement.appendChild(option);
      });
      // 设置默认选中项为 "Gemini"
      apiKeyTypeSelectElement.value = "Gemini";
      selectDiv.appendChild(apiKeyTypeSelectElement);
      return selectDiv;
    }

    /**
     * Creates and returns the panel title element.
     * @returns {HTMLHeadingElement} The panel title element.
     */
    function createPanelTitle() {
      const title = document.createElement("h3");
      title.textContent = "GitHub API 密钥查找器";
      return title;
    }

    /**
     * Creates and returns the button to toggle panel content visibility.
     * Also assigns the button to togglePanelContentButton.
     * @returns {HTMLButtonElement} The toggle button element.
     */
    function createPanelToggleButton() {
      togglePanelContentButton = createButton(
        panelContentVisible ? "隐藏面板" : "显示面板",
        handleTogglePanelContent,
        "toggle-panel-button" // Add this class
      );
      togglePanelContentButton.style.width = "100%";
      togglePanelContentButton.style.marginBottom = "10px";
      return togglePanelContentButton;
    }

    /**
     * Creates and returns the API key display area element.
     * Also assigns the element to keysDisplayElement.
     * @returns {HTMLDivElement} The key display area element.
     */
    function createKeyDisplayArea() {
      keysDisplayElement = document.createElement("div");
      keysDisplayElement.id = "apiKeyDisplayArea";
      keysDisplayElement.classList.add("panel-content-toggleable");
      keysDisplayElement.dataset.initialDisplay = "block";
      return keysDisplayElement;
    }

    /**
     * Creates and returns the container for the API key type selection dropdown.
     * Reuses createApiKeyTypeSelect and assigns the container to apiKeyTypeSelectContainer.
     * @returns {HTMLDivElement} The select container element.
     */
    function createApiKeySelectArea() {
      apiKeyTypeSelectContainer = createApiKeyTypeSelect();
      apiKeyTypeSelectContainer.classList.add("panel-content-toggleable");
      apiKeyTypeSelectContainer.dataset.initialDisplay = "block";
      return apiKeyTypeSelectContainer;
    }

    /**
     * Creates and returns the actions button area.
     * Includes buttons for running search, copying keys, clearing keys, and copying debug info.
     * @returns {HTMLDivElement} The actions area element.
     */
    function createActionsArea() {
      const actionsDiv = document.createElement("div");
      actionsDiv.className = "actions panel-content-toggleable";
      actionsDiv.dataset.initialDisplay = "flex";
      actionsDiv.style.textAlign = "center";
      searchButtonElement = createButton("搜索", handleRunSearch); // Assign to variable
      actionsDiv.appendChild(searchButtonElement); // Append the variable
      actionsDiv.appendChild(createButton("复制", handleCopyKeys));
      actionsDiv.appendChild(createButton("清空", handleClearKeys, "clear"));
      actionsDiv.appendChild(createButton("调试", handleCopyDebugInfo));
      return actionsDiv;
    }

    /**
     * Creates and returns the progress display element.
     * Also assigns the element to progressElement.
     * @returns {HTMLDivElement} The progress element.
     */
    function createProgressArea() {
      progressElement = document.createElement("div");
      progressElement.id = "apiKeyFinderProgress";
      progressElement.textContent = "准备就绪";
      progressElement.classList.add("panel-content-toggleable");
      progressElement.dataset.initialDisplay = "block";
      return progressElement;
    }

    /**
     * Creates and returns the status display element.
     * Also assigns the element to statusElement.
     * @returns {HTMLDivElement} The status element.
     */
    function createStatusArea() {
      statusElement = document.createElement("div");
      statusElement.id = "apiKeyFinderStatus";
      statusElement.classList.add("panel-content-toggleable");
      statusElement.dataset.initialDisplay = "block";
      return statusElement;
    }

    /**
     * Creates the main control panel UI.
     * Orchestrates the creation of all panel sub-elements.
     * @returns {HTMLDivElement} The main panel element.
     */
    function createControlPanel() {
      const panel = document.createElement("div");
      panel.id = "apiKeyFinderPanel";

      panel.appendChild(createPanelTitle());
      panel.appendChild(createPanelToggleButton());
      panel.appendChild(createKeyDisplayArea());
      panel.appendChild(createApiKeySelectArea());
      panel.appendChild(createActionsArea());
      panel.appendChild(createProgressArea());
      panel.appendChild(createStatusArea());

      // Initial visibility will be set in initialize() and updatePanelContentVisibility

      return panel;
    }

    /**
     * Toggles the visibility of the control panel.
     */
    function toggleControlPanel() {
      controlPanelVisible = !controlPanelVisible;
      if (controlPanelElement) {
        controlPanelElement.style.display = controlPanelVisible
          ? "flex"
          : "none";
        if (controlPanelVisible) {
          updateKeyDisplay(); // Refresh display when shown
          // Apply current panel content visibility state
          updatePanelContentVisibility();
        }
      }
    }

    /**
     * Updates the visibility of panel content elements based on panelContentVisible state.
     * Uses a common CSS class and dataset for initial display style.
     */
    function updatePanelContentVisibility() {
      if (togglePanelContentButton) {
        togglePanelContentButton.textContent = panelContentVisible
          ? "隐藏面板"
          : "显示面板";
      }

      const elementsToToggle = document.querySelectorAll(
        ".panel-content-toggleable"
      );
      elementsToToggle.forEach((el) => {
        if (panelContentVisible) {
          el.style.display = el.dataset.initialDisplay || "block"; // Fallback to 'block' if dataset not set
        } else {
          el.style.display = "none";
        }
      });
    }

    /**
     * Toggles the visibility of the main panel content (excluding the toggle button itself).
     */
    function handleTogglePanelContent() {
      panelContentVisible = !panelContentVisible;
      updatePanelContentVisibility();
    }

    // --- Button Action Handlers ---

    async function handleRunSearch() {
      if (searchButtonElement.textContent === "搜索") {
        // Start search
        searchButtonElement.textContent = "停止";
        searchButtonElement.style.backgroundColor = "#dc3545"; // Red color
        SearchLogic.setStopSearchFlag(false); // Reset stop flag for a new search

        SearchLogic.resetProgress();
        UI.updateProgressDisplay("准备开始搜索...");
        if (statusElement) statusElement.textContent = "正在准备搜索...";

        const selectedApiKeyType = apiKeyTypeSelectElement
          ? apiKeyTypeSelectElement.value
          : "ALL";
        debugLog(
          `UI: 手动执行搜索... 选择的类型: ${selectedApiKeyType}`,
          "general"
        );

        // Run the main search logic
        await SearchLogic.mainSearchLogic(selectedApiKeyType);

        // Search finished (either completed or stopped)
        UI.updateKeyDisplay(); // updateKeyDisplay now reads categorized keys

        const finalCategorizedKeys = SearchLogic.getStoredCategorizedKeys();
        let totalKeysCount = 0;
        for (const category in finalCategorizedKeys) {
          totalKeysCount += finalCategorizedKeys[category].length;
        }
        const newlyFoundKeys = GM_getValue("newlyFoundApiKeys", []);
        const finalMsg = SearchLogic.getStopSearchFlag()
          ? `搜索已停止！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundKeys.length} 个。`
          : `搜索完成！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundKeys.length} 个。`;

        if (statusElement) statusElement.textContent = finalMsg;
        UI.updateProgressDisplay("所有搜索流程完毕，准备就绪。"); // Final progress update
        alert(
          SearchLogic.getStopSearchFlag()
            ? "API 密钥搜索已停止！"
            : "API 密钥搜索完成！"
        );

        // Revert button state after search finishes
        searchButtonElement.textContent = "搜索";
        searchButtonElement.style.backgroundColor = "#0d6efd"; // Original blue color
      } else {
        // Stop search
        debugLog("UI: 手动停止搜索。", "general");
        SearchLogic.setStopSearchFlag(true); // Signal search to stop
        if (statusElement) statusElement.textContent = "正在停止搜索...";
        UI.updateProgressDisplay("正在停止...");

        // The mainSearchLogic and its helpers will detect stopSearchFlag and exit.
        // The button state will be reverted in the 'if' block after mainSearchLogic awaits.
      }
    }

    /**
     * Copies the newly found API keys from the current session to the clipboard.
     */
    function handleCopyKeys() {
      const newlyFoundKeys = GM_getValue("newlyFoundApiKeys", []); // Get keys found in the current session

      if (newlyFoundKeys.length > 0) {
        const clipboardContent = newlyFoundKeys.join("\n");
        GM_setClipboard(clipboardContent.trim(), "text");
        alert(`${newlyFoundKeys.length} 个本次新发现的密钥已复制到剪贴板！`);
        if (statusElement)
          statusElement.textContent = "本次新发现的密钥已复制到剪贴板。";
      } else {
        alert("本次运行没有发现新的密钥可供复制。");
        if (statusElement)
          statusElement.textContent = "本次运行没有发现新的密钥可供复制。";
      }
    }

    /**
     * Clears all stored AI service API keys (long-term and newly found) from GM_setValue.
     */
    function handleClearKeys() {
      if (
        confirm(
          "确定要清空所有已存储的 AI 服务 API 密钥和本次新发现的密钥吗？此操作不可撤销。"
        )
      ) {
        // Clear only the AI service categories (long-term storage)
        const categoriesToClear = [];
        SearchLogic.AI_SERVICES.forEach((service) => {
          categoriesToClear.push(
            `${service.name.charAt(0).toLowerCase()}${service.name.slice(
              1
            )}ApiKeys`
          );
        });
        [...new Set(categoriesToClear)].forEach((gmKey) =>
          GM_setValue(gmKey, [])
        );
        debugLog("[+] 清空了所有长期存储的 AI 服务密钥。", "general");

        // Clear the newly found keys storage
        GM_setValue("newlyFoundApiKeys", []);
        debugLog("[+] 清空了本次运行新发现的密钥存储。", "general");

        SearchLogic.clearCurrentSessionKeys(); // Also clear in-memory cumulative set

        updateKeyDisplay(); // Update UI to reflect cleared keys
        alert("所有已存储的 AI 服务 API 密钥和本次新发现的密钥已被清空。");
        if (statusElement) statusElement.textContent = "所有密钥已清空。";
      }
    }

    function handleCopyDebugInfo() {
      let clipboardContent = "--- 调试信息 ---\n";
      clipboardContent += "------------------------------------\n";
      let hasMessages = false;

      for (const categoryKey in debugMessages) {
        const category = debugMessages[categoryKey];
        if (categoryKey === "jsonExtract") {
          if (category.messages) {
            clipboardContent += `${category.title}:\n${category.messages}\n\n`;
            hasMessages = true;
          }
        } else if (category.messages && category.messages.length > 0) {
          clipboardContent +=
            `${category.title}:\n` + category.messages.join("\n") + "\n\n";
          hasMessages = true;
        }
      }

      if (!hasMessages) {
        clipboardContent += "暂无特定调试信息。点击“开始/重新搜索”以生成日志。";
      }

      if (hasMessages) {
        // Only copy if there's content
        GM_setClipboard(clipboardContent.trim(), "text");
        alert("调试信息已复制到剪贴板！");
        if (statusElement) statusElement.textContent = "调试信息已复制。";
      } else {
        alert("没有调试信息可供复制。");
        if (statusElement) statusElement.textContent = "没有调试信息可供复制。";
      }
    }

    /**
     * Updates the progress display element.
     * @param {string} text - The text to display.
     */
    function updateProgressDisplay(text) {
      if (progressElement) {
        progressElement.textContent = text;
      }
    }

    return {
      applyStyles,
      createControlPanel,
      toggleControlPanel,
      updateKeyDisplay,
      updateProgressDisplay,
    };
  })();

  // --- Search Logic Module ---
  const SearchLogic = (function () {
    // --- Configuration Constants ---
    const FILE_TYPES = [
      "language:C",
      "language:C#",
      "language:C++",
      "language:Go",
      "language:HTML",
      "language:Java",
      "language:JavaScript",
      "language:JSX",
      "language:Kotlin",
      "language:Markdown",
      "language:PowerShell",
      "language:Python",
      "language:SQL",
      "language:Shell",
      "language:Swift",
      "language:TypeScript",
      "path:*.backup",
      "path:*.bak",
      "path:*.cfg",
      "path:*.conf",
      "path:*.config",
      "path:*.enc",
      "path:*.env",
      "path:*.envrc",
      "path:*.ini",
      "path:*.ipynb",
      "path:*.json",
      "path:*.key",
      "path:*.log",
      "path:*.private",
      "path:*.prod",
      "path:*.properties",
      "path:*.secret",
      "path:*.tmp",
      "path:*.toml",
      "path:*.txt",
      "path:*.xml",
      "path:*.yaml",
      "path:*.yml",
      "path:.ipynb_checkpoints",
    ];
    const COMMON_KEYWORDS = [
      "access_key",
      "secret_key",
      "access_token",
      "api_key",
      "apikey",
      "api_secret",
      "apiSecret",
      "app_secret",
      "application_key",
      "app_key",
      "appkey",
      "auth_token",
      "authsecret",
      "api",
      "key",
    ];
    const API_KEY_PATTERNS = {
      OpenAI: [
        /sk-[a-zA-Z0-9]{48}/g,
        /sk-proj-[a-zA-Z0-9]{24}\.[a-zA-Z0-9]{24}/g,
      ],
      Gemini: [/AIzaSy[a-zA-Z0-9_-]{33}/g],
      Grok: [/gk_[a-zA-Z0-9]{60}/g],
      Claude: [/sk-ant-(?:api03-)?[a-zA-Z0-9\-_]{80,120}/g],
    };
    const AI_SERVICES = [
      { name: "OpenAI", keywords: ["openai api key", "sk-"] },
      {
        name: "Gemini",
        keywords: [
          // "(" + COMMON_KEYWORDS.join(" OR ") + ")",
          '("AIzaSy" AND (gemini OR aistudio OR chat OR prompt OR genai OR bearer OR token OR "v1beta") NOT (maps OR firebase OR android OR youtube OR adsense))',
        ],
      },
      { name: "Grok", keywords: ["grok api key", "gk_"] },
      {
        name: "Claude",
        keywords: ["claude api key", "anthropic api key", "sk-ant-"],
      },
    ];

    const SEARCH_SECTION = "code"; // Currently, only 'code' section is searched

    // --- Script State Variables ---
    let stopSearchFlag = false; // Flag to signal search stop
    const currentSessionKeys = new Set(); // Stores unique keys found during the current script execution

    // --- Statistics for Progress Tracking ---
    let totalFilesToProcess = 0;
    let processedFilesCount = 0;

    /**
     * Resets progress tracking variables.
     */
    function resetProgress() {
      totalFilesToProcess = 0;
      processedFilesCount = 0;
    }

    /**
     * Sets the stop search flag.
     * @param {boolean} value - The value to set the flag to.
     */
    function setStopSearchFlag(value) {
      stopSearchFlag = value;
    }

    /**
     * Gets the current value of the stop search flag.
     * @returns {boolean} The current value of the stop search flag.
     */
    function getStopSearchFlag() {
      return stopSearchFlag;
    }

    /**
     * Clears the current session keys set.
     */
    function clearCurrentSessionKeys() {
      currentSessionKeys.clear();
    }

    // --- Key Management Logic ---

    /**
     * Classifies a single API key based on known patterns, including specific AI services.
     * @param {string} key - The API key string.
     * @returns {string} The category of the key (e.g., "OpenAI", "Gemini", "Grok", "Claude").
     */
    function classifyKey(key) {
      if (typeof key !== "string") return "Other";

      for (const service of AI_SERVICES) {
        const patterns = API_KEY_PATTERNS[service.name];
        if (patterns) {
          for (const regex of patterns) {
            const testRegex = new RegExp(regex.source, regex.flags); // Ensure fresh regex for stateful flags like 'g'
            if (testRegex.test(key)) {
              return service.name; // This is the specific AI service name
            }
          }
        }
      }
      return "Other";
    }

    /**
     * Retrieves all stored API keys, categorized including specific AI services.
     * @returns {Object<string, string[]>} An object where keys are category names
     *                                     (e.g., "GitHub", "AWS", "OpenAI", "Other")
     *                                     and values are arrays of key strings.
     */
    function getStoredCategorizedKeys() {
      const categories = AI_SERVICES.map((service) => service.name); // Only include AI service categories

      const categorizedKeys = {};
      categories.forEach((category) => {
        // For AI services like OpenAI, Claude, etc.
        // Stored as, e.g., openaiApiKeys, geminiApiKeys
        const storageKey = `${category.charAt(0).toLowerCase()}${category.slice(
          1
        )}ApiKeys`;
        categorizedKeys[category] = GM_getValue(storageKey, []);
      });
      // Optionally, you might want to explicitly add 'Other' if needed elsewhere,
      // but based on the task, we only care about the 4 AI types for storage/retrieval.
      // categorizedKeys["Other"] = GM_getValue("otherApiKeys", []); // Removed as per task

      return categorizedKeys;
    }

    // --- Core Search Logic ---

    /**
     * Processes a single search result item to extract raw file URL and fetch content.
     * @param {object} item - The search result item object.
     * @param {string} serviceName - The name of the AI service being searched for.
     * @returns {Promise<void>}
     */
    async function processSearchResultItem(item, serviceName) {
      if (stopSearchFlag) {
        debugLog(`停止标志已设置，跳过处理搜索结果项: ${item.path}`, "general");
        return;
      }

      const repoNwo = item.repo_nwo;
      const filePath = item.path;
      const commitSha = item.commit_sha || item.ref_name; // Fallback to ref_name (branch/tag)

      if (repoNwo && filePath && commitSha) {
        const rawUrl = `https://raw.githubusercontent.com/${repoNwo}/${commitSha}/${filePath}`;
        debugLog(
          `构造 Raw URL: ${rawUrl} (repo: ${repoNwo}, path: ${filePath}, commit: ${commitSha})`,
          "rawUrlConstructed"
        );
        await fetchAndProcessRawFile(rawUrl, serviceName, filePath);
      } else {
        debugLog(
          `搜索结果条目信息不全，无法构造 Raw URL: ${JSON.stringify(
            item
          ).substring(0, 200)}...`,
          "error"
        );
        // Increment processedFilesCount even if we can't process the item
        processedFilesCount++;
        UI.updateProgressDisplay(
          `已跳过 (信息不全): ${processedFilesCount} / ${totalFilesToProcess} - ${
            filePath ? filePath.split("/").pop() : "未知文件"
          }`
        );
      }
    }

    /**
     * Handles the logic for found potential keys: deduplication and storage.
     * @param {string[]} potentialKeys - An array of potential key strings found in a file.
     * @param {string} serviceName - The name of the AI service the keys belong to.
     */
    function handleFoundKeys(potentialKeys, serviceName) {
      if (potentialKeys.length === 0) {
        debugLog(`未找到 ${serviceName} 的潜在密钥。`, "general");
        return;
      }

      debugLog(
        `找到 ${
          potentialKeys.length
        } 个潜在密钥为 ${serviceName}: ${potentialKeys.join(", ")}`,
        "general"
      );

      // Get current long-term stored keys for this service
      const longTermStorageKey = `${serviceName
        .charAt(0)
        .toLowerCase()}${serviceName.slice(1)}ApiKeys`;
      let longTermKeys = GM_getValue(longTermStorageKey, []);
      const longTermKeysSet = new Set(longTermKeys); // Use a Set for efficient lookup

      // Get current newly found keys for this session
      let newlyFoundKeys = GM_getValue("newlyFoundApiKeys", []);
      const newlyFoundKeysSet = new Set(newlyFoundKeys); // Use a Set for efficient lookup

      let newKeysFoundInThisFile = 0;

      potentialKeys.forEach((key) => {
        // Check if the key is already in the long-term storage
        if (!longTermKeysSet.has(key)) {
          // It's a new key not seen before (in long-term storage)
          debugLog(`发现新密钥 (不在长期存储中): ${key}`, "general");

          // Add to long-term storage (in-memory array and Set)
          longTermKeys.push(key);
          longTermKeysSet.add(key); // Update the Set for subsequent checks in this file

          // Add to newly found keys for this session (in-memory array and Set)
          if (!newlyFoundKeysSet.has(key)) {
            // Ensure it's not already added in this session from another file
            newlyFoundKeys.push(key);
            newlyFoundKeysSet.add(key);
          }

          // Add to the cumulative session set (used by UI update)
          currentSessionKeys.add(key);

          newKeysFoundInThisFile++;
        } else {
          debugLog(`密钥已存在于长期存储中，忽略: ${key}`, "general");
        }
      });

      // If any new keys were found, update storage and UI
      if (newKeysFoundInThisFile > 0) {
        debugLog(
          `找到 ${newKeysFoundInThisFile} 个新密钥，更新存储。`,
          "general"
        );
        // Update long-term storage for this category
        GM_setValue(longTermStorageKey, longTermKeys);
        debugLog(
          `更新了长期存储 (${longTermStorageKey})，总数: ${longTermKeys.length}`,
          "general"
        );

        // Update newly found keys storage for this session
        GM_setValue("newlyFoundApiKeys", newlyFoundKeys);
        debugLog(
          `更新了本次新发现密钥存储，总数: ${newlyFoundKeys.length}`,
          "general"
        );

        // Update the UI display immediately to show the new total count
        UI.updateKeyDisplay();
      } else {
        debugLog(`未找到新密钥。`, "general");
      }
    }

    /**
     * Constructs a GitHub search URL.
     * @param {string[]} serviceKeywords - Keywords for the service.
     * @param {string} section - The search section (e.g., "code").
     * @param {number} page - The page number for pagination.
     * @returns {string} The constructed search URL.
     */
    function constructSearchURL(serviceKeywords, fileType, section, page = 1) {
      const query =
        "(" + fileType + ")" + " AND " + serviceKeywords.join(" AND "); // Add quotes for exact phrase matching
      const encodedQuery = encodeURIComponent(query).replace(/%20/g, "+"); // Replace %20 with + for GitHub search
      const query_url = `https://github.com/search?q=${encodedQuery}&type=${section}&p=${page}`;
      return query_url;
    }

    /**
     * Extracts potential API keys from HTML content based on service-specific patterns.
     * @param {string} textContent - The text content to search within.
     * @param {string} serviceName - The name of the AI service.
     * @returns {string[]} An array of found potential keys.
     */
    function extractPotentialKeys(textContent, serviceName) {
      const patterns = API_KEY_PATTERNS[serviceName];
      if (!patterns) return [];

      let foundKeys = new Set(); // Use a Set to automatically handle duplicates within this pass
      patterns.forEach((pattern) => {
        const currentRegex = new RegExp(pattern.source, pattern.flags); // Create a new instance
        let match;
        while ((match = currentRegex.exec(textContent)) !== null) {
          // Use the new instance
          foundKeys.add(match[0]);
        }
      });
      return Array.from(foundKeys);
    }

    /**
     * Fetches and processes a single raw file content from GitHub.
     * @param {string} rawUrl - The URL of the raw file.
     * @param {string} serviceName - The name of the AI service being searched for.
     * @param {string} filePath - The path of the file (for logging).
     * @returns {Promise<void>}
     */
    async function fetchAndProcessRawFile(rawUrl, serviceName, filePath) {
      // Check stop flag before making the request
      if (stopSearchFlag) {
        debugLog(`停止标志已设置，跳过获取文件: ${filePath}`, "general");
        processedFilesCount++; // Still count it as processed to update progress correctly
        UI.updateProgressDisplay(
          `已跳过 (停止): ${processedFilesCount} / ${totalFilesToProcess} - ${filePath
            .split("/")
            .pop()}`
        );
        return Promise.resolve(); // Resolve immediately if stopping
      }

      debugLog(`开始获取原始文件: ${rawUrl}`, "fileFetch");
      const fileName = filePath.split("/").pop();
      UI.updateProgressDisplay(
        `尝试获取: ${
          processedFilesCount + 1
        } / ${totalFilesToProcess} - ${fileName}`
      );

      try {
        const response = await promisifiedRequest({
          method: "GET",
          url: rawUrl,
          timeout: 10000, // Add 10-second timeout
        });

        // Check stop flag after successful request but before processing
        if (stopSearchFlag) {
          debugLog(
            `停止标志已设置，获取成功但跳过处理文件: ${filePath}`,
            "general"
          );
          // processedFilesCount is incremented below in finally
          // UI.updateProgressDisplay is done in finally
          return; // Exit the async function
        }

        processedFilesCount++;
        if (response.status >= 200 && response.status < 300) {
          const rawContent = response.responseText;
          debugLog(
            `成功获取 ${filePath} (前200字符): ${rawContent.substring(
              0,
              200
            )}...`,
            "fileFetch"
          );
          const potentialKeys = extractPotentialKeys(rawContent, serviceName);
          handleFoundKeys(potentialKeys, serviceName);
        } else {
          debugLog(
            `获取 ${filePath} 失败。状态: ${response.status}, URL: ${rawUrl}`,
            "error"
          );
        }
      } catch (error) {
        // Handle network errors, timeouts, etc.
        if (stopSearchFlag) {
          debugLog(
            `停止标志已设置，获取出错但跳过处理文件: ${filePath}`,
            "general"
          );
          // processedFilesCount is incremented below in finally
          // UI.updateProgressDisplay is done in finally
          return; // Exit the async function
        }
        debugLog(
          `获取 ${filePath} 时出错. URL: ${rawUrl}, 错误: ${
            error.statusText || error.message || "未知"
          }`,
          "error"
        );
        // processedFilesCount is incremented below in finally
        // UI.updateProgressDisplay is done in finally
      } finally {
        // Always increment processedFilesCount and update progress
        processedFilesCount++;
        UI.updateProgressDisplay(
          `已处理: ${processedFilesCount} / ${totalFilesToProcess} - ${fileName}`
        );
      }
    }

    /**
     * Performs a search for a given service in a specific section and page.
     * Extracts raw file URLs from search results and fetches their content.
     * @param {object} service - The AI service object.
     * @param {string} section - The search section.
     * @param {number} page - The page number.
     * @returns {Promise<boolean>} Resolves with true if search should continue, false if stopped or error.
     */
    async function performSingleSearch(service, fileType, section, page = 1) {
      if (stopSearchFlag) {
        debugLog(
          `停止标志已设置，跳过搜索 ${service.name} ${fileType} 于 ${section} (页 ${page})`,
          "general"
        );
        return false; // Indicate that search should stop
      }

      debugLog(
        `开始搜索 ${service.name} ${fileType} 于 ${section} (页 ${page})`,
        "general"
      );
      const searchUrl = constructSearchURL(
        service.keywords,
        fileType,
        section,
        page
      );
      debugLog(`搜索 URL: ${searchUrl}`, "general");

      try {
        const response = await promisifiedRequest({
          method: "GET",
          url: searchUrl,
          // No timeout specified in original GM_xmlhttpRequest for search page
        });

        // Check stop flag after successful request but before processing
        if (stopSearchFlag) {
          debugLog(
            `停止标志已设置，获取成功但跳过处理搜索结果页: ${searchUrl}`,
            "general"
          );
          return false; // Indicate that search should stop
        }

        if (response.status >= 200 && response.status < 300) {
          const htmlContent = response.responseText;
          debugLog(
            `GitHub 搜索结果 (前200字符): ${htmlContent.substring(0, 200)}...`,
            "general"
          );

          let embeddedData = null;
          try {
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, "text/html");
            const scriptElement = doc.querySelector(
              'script[type="application/json"][data-target="react-app.embeddedData"]'
            );
            if (scriptElement) {
              embeddedData = JSON.parse(scriptElement.textContent);
              debugLog(
                {
                  message: "成功提取并解析 embeddedData JSON",
                  data: embeddedData,
                },
                "jsonExtract"
              );
            } else {
              debugLog(
                '未找到 <script data-target="react-app.embeddedData">。',
                "error"
              );
            }
          } catch (e) {
            debugLog(`解析 embeddedData JSON 失败: ${e.message}`, "error");
          }

          const rawFilePromises = [];
          if (embeddedData?.payload?.results) {
            debugLog(
              `从 embeddedData 找到 ${embeddedData.payload.results.length} 个结果。`,
              "general"
            );
            // Process each search result item
            for (const item of embeddedData.payload.results) {
              // IMPORTANT: This is the request delay logic.
              await new Promise((resolveDelay) =>
                setTimeout(resolveDelay, 2000 + Math.random() * 2000)
              );

              // Check stop flag before processing the next item
              if (stopSearchFlag) {
                debugLog(
                  `停止标志已设置，跳过处理搜索结果项: ${item.path}`,
                  "general"
                );
                break; // Exit the loop early
              }
              // Increment total files to process BEFORE processing the item
              totalFilesToProcess++;
              await processSearchResultItem(item, service.name);
            }
          } else {
            debugLog(
              "embeddedData 中未找到 payload.results 或结构不符。",
              "general"
            );
          }

          // After processing all items in this page's results
          if (!stopSearchFlag && embeddedData?.payload?.results?.length > 0) {
            debugLog(`当前页 (${page}) 的所有搜索结果项处理完成。`, "general");
            // The totalFilesToProcess and processedFilesCount are updated within processSearchResultItem/fetchAndProcessRawFile
          } else if (
            !stopSearchFlag &&
            embeddedData?.payload?.results?.length === 0
          ) {
            debugLog(`当前页 (${page}) 未找到搜索结果项。`, "general");
          } else if (stopSearchFlag) {
            debugLog(
              `因停止标志设置，当前页 (${page}) 的搜索结果处理被中断。`,
              "general"
            );
          }
          return !stopSearchFlag; // Resolve with false if stop flag is set
        } else {
          debugLog(
            `获取 GitHub 搜索页失败. 状态: ${response.status}, URL: ${searchUrl}`,
            "error"
          );
          return false; // Indicate search page fetch failure
        }
      } catch (error) {
        // Handle network errors from promisifiedRequest
        if (stopSearchFlag) {
          debugLog(
            `停止标志已设置，获取 GitHub 搜索页时中断. URL: ${searchUrl}`,
            "general"
          );
        } else {
          debugLog(
            `获取 GitHub 搜索页时网络错误. URL: ${searchUrl}, 错误: ${
              error.statusText || error.message || "未知"
            }`,
            "error"
          );
        }
        return false; // Indicate failure or stop
      }
    }

    /**
     * Main logic for the API key search.
     * @param {string} selectedApiKeyType - The type of API key to search for ("ALL" or specific service name).
     */
    async function mainSearchLogic(selectedApiKeyType = "ALL") {
      debugLog(
        `[+] GitHub API 密钥查找器脚本启动。搜索类型: ${selectedApiKeyType}`,
        "general"
      );

      // 1. Load existing keys from storage into the current session
      const storedKeys = getStoredCategorizedKeys();
      let loadedKeyCount = 0;
      for (const category in storedKeys) {
        storedKeys[category].forEach((key) => {
          currentSessionKeys.add(key);
          loadedKeyCount++;
        });
      }
      if (loadedKeyCount > 0) {
        debugLog(`[+] 从本地存储加载了 ${loadedKeyCount} 个密钥。`, "general");
      }

      // 2. Determine which services to search
      let servicesToSearch = AI_SERVICES;
      if (selectedApiKeyType !== "ALL") {
        servicesToSearch = AI_SERVICES.filter(
          (s) => s.name === selectedApiKeyType
        );
        if (servicesToSearch.length === 0) {
          debugLog(
            `[!] 未找到 "${selectedApiKeyType}" 服务配置，将搜索全部类型。`,
            "error"
          );
          servicesToSearch = AI_SERVICES; // Fallback
        }
      }

      // 3. Perform searches
      for (const service of servicesToSearch) {
        if (stopSearchFlag) {
          debugLog("停止标志已设置，跳过服务搜索。", "general");
          break; // Exit service loop
        }
        const section = SEARCH_SECTION; // Use the constant directly
        for (const fileType of FILE_TYPES) {
          if (stopSearchFlag) {
            debugLog("停止标志已设置，跳过文件类型搜索。", "general");
            break; // Exit file type loop
          }
          // Loop through pages 1 to 5
          for (let page = 1; page <= 5; page++) {
            if (stopSearchFlag) {
              debugLog("停止标志已设置，跳过页面搜索。", "general");
              break; // Exit page loop
            }

            // Status update is now handled by UI module
            // if (statusElement) statusElement.textContent = `正在搜索 ${service.name} ${fileType} 于 ${section} (页 ${page})...`;
            // updateProgressDisplay(`开始搜索 ${service.name} 于 ${section} (页 ${page})...`);

            try {
              // IMPORTANT: This is the request delay logic.
              await new Promise((resolveDelay) => {
                if (stopSearchFlag) {
                  debugLog("停止标志已设置，跳过延迟。", "general");
                  resolveDelay(); // Resolve immediately if stopping
                } else {
                  setTimeout(resolveDelay, 2000 + Math.random() * 2000); // Calculate random delay here
                }
              });

              if (stopSearchFlag) {
                debugLog(
                  "停止标志已设置，跳过 performSingleSearch。",
                  "general"
                );
                break; // Exit page loop
              }

              const success = await performSingleSearch(
                service,
                fileType,
                section,
                page
              );
              if (!success) {
                debugLog(
                  `搜索 ${service.name} ${fileType} 在 ${section} (页 ${page}) 失败或已停止，停止当前页循环。`,
                  "general"
                );
                break; // Stop the current page loop
              }
            } catch (error) {
              if (stopSearchFlag) {
                debugLog(
                  `停止标志已设置，搜索 ${service.name} ${fileType} 在 ${section} (页 ${page}) 时中断。`,
                  "general"
                );
              } else {
                debugLog(
                  `搜索 ${
                    service.name
                  } ${fileType} 在 ${section} (页 ${page}) 时发生未捕获错误: ${
                    error.message || error
                  }`,
                  "error"
                );
              }
              // Even if there's an unexpected error here, we should probably stop the page loop
              break;
            }
          } // End page loop
        } // End file type loop
      } // End service loop

      if (stopSearchFlag) {
        debugLog("[+] 搜索任务因停止标志设置而提前结束。", "general");
      } else {
        debugLog("[+] 所有搜索任务已处理完毕。", "general");
      }

      // Saving is now handled incrementally in fetchAndProcessRawFile.
      // We only need to update the final status message here.

      // Final status update is now handled by UI module
      // const finalCategorizedKeys = getStoredCategorizedKeys();
      // let totalKeysCount = 0;
      // for (const category in finalCategorizedKeys) {
      //   totalKeysCount += finalCategorizedKeys[category].length;
      // }
      // const newlyFoundKeys = GM_getValue("newlyFoundApiKeys", []);
      // if (statusElement) {
      //   statusElement.textContent = stopSearchFlag
      //     ? `搜索已停止！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundKeys.length} 个。`
      //     : `搜索完成！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundKeys.length} 个。`;
      // }
      debugLog("[+] GitHub API 密钥查找器脚本结束。", "general");
    }

    return {
      FILE_TYPES,
      COMMON_KEYWORDS,
      API_KEY_PATTERNS,
      AI_SERVICES,
      SEARCH_SECTION,
      resetProgress,
      setStopSearchFlag,
      getStopSearchFlag,
      clearCurrentSessionKeys,
      classifyKey,
      getStoredCategorizedKeys,
      mainSearchLogic,
    };
  })();

  // --- Initialization ---
  function initialize() {
    // Clear newly found keys from the previous session at the start of the script
    GM_setValue("newlyFoundApiKeys", []);
    debugLog("[+] 清空了本次运行新发现的密钥存储。", "general");

    UI.applyStyles();
    const controlPanelElement = UI.createControlPanel(); // This now also sets up panelContentElements
    document.body.appendChild(controlPanelElement);
    GM_registerMenuCommand("切换 API 密钥查找器面板", UI.toggleControlPanel);
    UI.updateKeyDisplay(); // Initial display of keys

    // Set initial panel content visibility
    // UI.panelContentVisible = false; // Default to hide content - This state is now managed within the UI module
    // UI.updatePanelContentVisibility(); // Apply the visibility state

    debugLog("API 密钥查找器 UI 已初始化。", "general");
  }

  // Ensure script runs after the DOM is fully loaded
  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    initialize();
  } else {
    window.addEventListener("DOMContentLoaded", initialize);
  }
})();
