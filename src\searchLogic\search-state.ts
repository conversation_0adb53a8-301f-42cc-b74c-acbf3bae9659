import type { ISearchState } from "../types";

/**
 * Manages the state of the search operation
 */
export class GitHubSearchState implements ISearchState {
    public stopSearchFlag: boolean = false;
    public currentSessionKeys: Set<string> = new Set();
    public totalFilesToProcess: number = 0;
    public processedFilesCount: number = 0;

    /**
     * Resets progress tracking counters
     */
    public resetProgress(): void {
        this.totalFilesToProcess = 0;
        this.processedFilesCount = 0;
    }

    /**
     * Sets the stop search flag
     */
    public setStopSearchFlag(value: boolean): void {
        this.stopSearchFlag = value;
    }

    /**
     * Gets the current stop search flag value
     */
    public getStopSearchFlag(): boolean {
        return this.stopSearchFlag;
    }

    /**
     * Clears all current session keys
     */
    public clearCurrentSessionKeys(): void {
        this.currentSessionKeys.clear();
    }

    /**
     * Adds a key to the current session
     */
    public addCurrentSessionKey(key: string): void {
        this.currentSessionKeys.add(key);
    }

    /**
     * Gets all current session keys
     */
    public getCurrentSessionKeys(): Set<string> {
        return this.currentSessionKeys;
    }

    /**
     * Sets the total number of files to process
     */
    public setTotalFilesToProcess(value: number): void {
        this.totalFilesToProcess = value;
    }

    /**
     * Sets the number of processed files
     */
    public setProcessedFilesCount(value: number): void {
        this.processedFilesCount = value;
    }

    /**
     * Gets the total number of files to process
     */
    public getTotalFilesToProcess(): number {
        return this.totalFilesToProcess;
    }

    /**
     * Gets the number of processed files
     */
    public getProcessedFilesCount(): number {
        return this.processedFilesCount;
    }
}

export default GitHubSearchState;