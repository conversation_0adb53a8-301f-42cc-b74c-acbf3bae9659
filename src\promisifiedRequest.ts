import { GM_xmlhttpRequest } from "$";
import type { IHttpRequestOptions, IHttpResponse } from "./types";

/**
 * Promisified wrapper for GM_xmlhttpRequest
 */
export function promisifiedRequest(options: IHttpRequestOptions): Promise<IHttpResponse> {
  return new Promise((resolve, reject) => {
    GM_xmlhttpRequest({
      ...options,
      onload: function (response: IHttpResponse) {
        resolve(response);
      },
      onerror: function (error: any) {
        reject(error);
      },
      ontimeout: function () {
        reject(new Error("Request timeout"));
      },
    });
  });
}

export default promisifiedRequest;
