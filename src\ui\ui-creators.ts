// ui-creators.ts
// 负责 UI 元素的创建

import PANEL_STYLES from '../styles';
import { AI_SERVICES } from "../constants";
import { UIElements } from "./ui-elements";
import { UIHandlers } from './ui-handlers';// 负责 UI 元素的创建

export class UICreators {
    /**
     * Applies CSS styles for the control panel to the document head.
     */
    static applyStyles = (): void => {
        const style: HTMLStyleElement = document.createElement("style");
        style.textContent = PANEL_STYLES;
        document.head.appendChild(style);
    }

    /**
     * Creates and returns a button element.
     * @param {string} text - The button text.
     * @param {function} onClickHandler - The event handler for button click.
     * @param {string} [className] - Optional CSS class name.
     * @returns {HTMLButtonElement} The created button element.
     */
    static createButton = (text: string, onClickHandler: () => void, className?: string): HTMLButtonElement => {
        const button: HTMLButtonElement = document.createElement("button");
        button.textContent = text;
        button.addEventListener("click", onClickHandler);
        if (className) {
            button.className = className;
        }
        return button;
    }

    /**
     * Creates the API type selection dropdown.
     * @returns {HTMLDivElement} The div containing the label and select element.
     */
    static createApiKeyTypeSelect = (): HTMLDivElement => {
        const selectDiv: HTMLDivElement = document.createElement("div");
        selectDiv.style.marginBottom = "10px";
        selectDiv.style.textAlign = "center";

        const label: HTMLLabelElement = document.createElement("label");
        label.textContent = "选择API类型: ";
        label.htmlFor = "apiKeyTypeSelect";
        selectDiv.appendChild(label);

        const selectElement = document.createElement("select");
        UIElements.apiKeyTypeSelectElement = selectElement;
        selectElement.id = "apiKeyTypeSelect";
        selectElement.style.padding = "5px";
        selectElement.style.borderRadius = "3px";

        const allOption: HTMLOptionElement = document.createElement("option");
        allOption.value = "ALL";
        allOption.textContent = "全部类型";
        selectElement.appendChild(allOption);

        AI_SERVICES.forEach((service: { name: string }) => {
            const option: HTMLOptionElement = document.createElement("option");
            option.value = service.name;
            option.textContent = service.name;
            selectElement.appendChild(option);
        });
        // 设置默认选中项为 "Gemini"
        selectElement.value = "Gemini";
        selectDiv.appendChild(selectElement);
        return selectDiv;
    }

    /**
     * Creates and returns the panel title element.
     * @returns {HTMLHeadingElement} The panel title element.
     */
    static createPanelTitle = (): HTMLHeadingElement => {
        const title: HTMLHeadingElement = document.createElement("h3");
        title.textContent = "GitHub API 密钥查找器";
        return title;
    }

    /**
     * Creates and returns the button to toggle panel content visibility.
     * Also assigns the button to togglePanelContentButton.
     */
    static createPanelToggleButton = (): HTMLButtonElement => {
        const button = UICreators.createButton(
            UIElements.panelContentVisible ? "隐藏面板" : "显示面板",
            UIHandlers.handleTogglePanelContent,
            "toggle-panel-button"
        );
        UIElements.togglePanelContentButton = button;
        button.style.width = "100%";
        button.style.marginBottom = "10px";
        return button;
    }

    /**
     * Creates and returns the API key display area element.
     * Also assigns the element to keysDisplayElement.
     */
    static createKeyDisplayArea = (): HTMLDivElement => {
        const div = document.createElement("div");
        UIElements.keysDisplayElement = div;
        div.id = "apiKeyDisplayArea";
        div.classList.add("panel-content-toggleable");
        div.dataset.initialDisplay = "block";
        return div;
    }

    /**
     * Creates and returns the container for the API key type selection dropdown.
     * Reuses createApiKeyTypeSelect and assigns the container to apiKeyTypeSelectContainer.
     */
    static createApiKeySelectArea = (): HTMLDivElement => {
        const container = UICreators.createApiKeyTypeSelect();
        UIElements.apiKeyTypeSelectContainer = container;
        container.classList.add("panel-content-toggleable");
        container.dataset.initialDisplay = "block";
        return container;
    }

    /**
     * Creates and returns the actions button area.
     * Includes buttons for running search, copying keys, clearing keys, and copying debug info.
     */
    static createActionsArea = (): HTMLDivElement => {
        const actionsDiv = document.createElement("div");
        actionsDiv.className = "actions panel-content-toggleable";
        actionsDiv.dataset.initialDisplay = "flex";
        actionsDiv.style.textAlign = "center";

        const searchButton = UICreators.createButton("搜索", UIHandlers.handleRunSearch, undefined);
        UIElements.searchButtonElement = searchButton;
        actionsDiv.appendChild(searchButton);
        actionsDiv.appendChild(UICreators.createButton("复制", UIHandlers.handleCopyKeys, undefined));
        actionsDiv.appendChild(UICreators.createButton("清空", UIHandlers.handleClearKeys, "clear"));
        actionsDiv.appendChild(UICreators.createButton("调试", UIHandlers.handleCopyDebugInfo, undefined));
        return actionsDiv;
    }

    /**
     * Creates and returns the progress display element.
     * Also assigns the element to progressElement.
     */
    static createProgressArea = (): HTMLDivElement => {
        const div = document.createElement("div");
        UIElements.progressElement = div;
        div.id = "apiKeyFinderProgress";
        div.textContent = "准备就绪";
        div.classList.add("panel-content-toggleable");
        div.dataset.initialDisplay = "block";
        return div;
    }

    /**
     * Creates and returns the status display element.
     * Also assigns the element to statusElement.
     */
    static createStatusArea = (): HTMLDivElement => {
        const div = document.createElement("div");
        UIElements.statusElement = div;
        div.id = "apiKeyFinderStatus";
        div.classList.add("panel-content-toggleable");
        div.dataset.initialDisplay = "block";
        return div;
    }
}
