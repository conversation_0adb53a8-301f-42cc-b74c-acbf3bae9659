import type { IUIElements } from "../types";

/**
 * Manages UI element references and state variables
 */
export class GitHubKeyFinderUIElements implements IUIElements {
    // --- UI State Variables ---
    public controlPanelVisible: boolean = true;
    public panelContentVisible: boolean = true;

    // --- UI Element References ---
    public controlPanelElement: HTMLElement | null = null;
    public keysDisplayElement: HTMLElement | null = null;
    public statusElement: HTMLElement | null = null;
    public apiKeyTypeSelectElement: HTMLSelectElement | null = null;
    public apiKeyTypeSelectContainer: HTMLElement | null = null;
    public progressElement: HTMLElement | null = null;
    public togglePanelContentButton: HTMLElement | null = null;
    public searchButtonElement: HTMLElement | null = null;
}

// Export singleton instance
export const UIElements = new GitHubKeyFinderUIElements();