# 项目名称: GitHub API Key Finder

**版本:** 0.3
**作者:** You
**描述:** Automatically search for AI service API keys on GitHub with a UI.

## 功能描述

该 Tampermonkey 用户脚本的核心功能是自动化地在 GitHub 平台上搜索和管理多种 API 密钥。它能够根据预设的关键词和正则表达式，针对特定的 AI 服务（如 OpenAI, Gemini, Claude, Grok）以及通用的 GitHub 和 AWS 密钥，在 GitHub 的代码搜索结果中查找潜在的 API 密钥。脚本提供了一个可交互的用户界面（UI），用户可以通过该界面手动触发特定类型或全部类型的密钥搜索，查看已分类存储的密钥，一键复制所有密钥，以及清空本地存储的密钥。此外，UI 还提供了调试信息显示和操作进度反馈功能。脚本会将发现的密钥进行分类、去重，并使用 Tampermonkey 的 API 进行本地持久化存储。为了提高搜索的隐蔽性并降低被目标网站限制的风险，脚本在执行搜索操作时会引入随机延时。
## 技术细节

*   **命名空间:** http://tampermonkey.net/
*   **匹配页面:** *://github.com/*
*   **所需权限 (Grants):**
    *   GM_xmlhttpRequest
    *   GM_setValue
    *   GM_getValue
    *   GM_registerMenuCommand
    *   GM_setClipboard