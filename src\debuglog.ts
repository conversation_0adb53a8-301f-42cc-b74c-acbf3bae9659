
import type { DebugLogType } from "./types";

/**
 * Debug logging utility for the GitHub API Key Finder
 */

const MAX_DEBUG_MESSAGES_PER_CATEGORY = 200;

interface IDebugMessage {
    title: string;
    messages: string[] | string | null;
}

interface IDebugMessages {
    [key: string]: IDebugMessage;
}

const debugMessages: IDebugMessages = {
    general: { title: "通用日志", messages: [] },
    jsonExtract: { title: "提取的 JSON (片段)", messages: null },
    rawUrlConstructed: { title: "构造的 Raw URLs", messages: [] },
    fileFetch: { title: "Raw 文件获取状态", messages: [] },
    error: { title: "错误信息", messages: [] },
};

/**
 * Logs a debug message to the console and updates the debugMessages object
 */
export function debugLog(message: string | object, type: DebugLogType = "general"): void {
    // Ensure type corresponds to a key in debugMessages
    console.log(`[DEBUG-${type.toUpperCase()}]:`, message); // Keep detailed console output

    const timestamp = new Date().toLocaleTimeString();
    const formattedMessage = `[${timestamp}] ${typeof message === "object" ? JSON.stringify(message, null, 2) : message
        }`;

    if (debugMessages[type]) {
        if (type === "jsonExtract") {
            // Handle the special 'jsonExtract' case which stores a single formatted string (or null)
            if (typeof message === "object" && (message as any).data) {
                debugMessages.jsonExtract.messages = `[${timestamp}] ${JSON.stringify(
                    (message as any).data,
                    null,
                    2
                ).substring(0, 1000)}... (截断)`;
            } else {
                // Store the raw message if it's not in the expected object format
                debugMessages.jsonExtract.messages = formattedMessage;
            }
        } else if (Array.isArray(debugMessages[type].messages)) {
            debugMessages[type].messages.push(formattedMessage);
            // Limit the number of messages
            if (
                debugMessages[type].messages.length > MAX_DEBUG_MESSAGES_PER_CATEGORY
            ) {
                debugMessages[type].messages.shift(); // Remove the oldest message
            }
        } else {
            // Fallback for misconfigured types, log to general
            console.warn(
                `[DEBUG] Invalid debug message type configuration for: ${type}. Logging to general.`
            );
            if (Array.isArray(debugMessages.general.messages)) {
                debugMessages.general.messages.push(
                    `[${type}-UNHANDLED] ${formattedMessage}`
                );
            }
        }
    } else {
        console.warn(
            `[DEBUG] Unknown debug message type: ${type}. Logging to general.`
        );
        if (Array.isArray(debugMessages.general.messages)) {
            debugMessages.general.messages.push(
                `[${type}-UNKNOWN] ${formattedMessage}`
            );
        }
    }
}