import { defineConfig } from "vite";
import monkey from "vite-plugin-monkey";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    monkey({
      entry: "src/main.ts",
      userscript: {
        name: {
          en: "Github api keys finder",
          zh: "Github API 密钥查找器",
        },
        namespace: "https://github.com/lll9p",
        version: "0.0.1",
        icon: "https://vitejs.dev/logo.svg",
        description: {
          "": "A userscript to find Github API keys in the current page.",
          zh: "在当前页面查找 Github API 密钥的用户脚本。",
          en: "A userscript to find Github API keys in the current page.",
        },
        // match: [/^https:\/\/.*\.github\.com\/.*/],
        match: ["*.github.com/*"],
      },
    }),
  ],
});
