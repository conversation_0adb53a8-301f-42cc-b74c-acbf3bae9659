import type { ISearchUrlBuilder } from "../types";

/**
 * Builds GitHub search URLs for API key discovery
 */
export class GitHubSearchUrlBuilder implements ISearchUrlBuilder {
    /**
     * Constructs a GitHub search URL.
     */
    public constructSearchURL(
        serviceKeywords: readonly string[],
        fileType: string,
        section: string,
        page: number = 1
    ): string {
        const query = `(${fileType}) AND ${serviceKeywords.join(" AND ")}`;
        const encodedQuery = encodeURIComponent(query).replace(/%20/g, "+");
        return `https://github.com/search?q=${encodedQuery}&type=${section}&p=${page}`;
    }
}

export default GitHubSearchUrlBuilder;