// ui-updaters.ts
// 负责 UI 元素的状态更新

import { GM_getValue } from '$';
import { SearchLogic } from "../searchLogic";
import { AI_SERVICES } from "../constants";
import { UIElements } from "./ui-elements";

export class UIUpdaters {
    /**
     * Updates the display area with the total count of stored API keys and newly found keys.
     */
    static updateKeyDisplay = (): void => {
        if (!UIElements.keysDisplayElement) return;

        const categorizedKeys: { [key: string]: string[] } = SearchLogic.getStoredCategorizedKeys(); // Long-term storage
        const newlyFoundKeys: string[] = GM_getValue("newlyFoundApiKeys", []) as string[]; // Newly found in this session

        UIElements.keysDisplayElement.innerHTML = ""; // Clear previous keys

        let totalLongTermKeyCount: number = 0;
        const categoryCounts: { [key: string]: number } = {};

        // Calculate total long-term count and category counts
        AI_SERVICES.forEach((service: { name: string }) => {
            const serviceKeys: string[] = categorizedKeys[service.name] || [];
            totalLongTermKeyCount += serviceKeys.length;
            categoryCounts[service.name] = serviceKeys.length;
        });

        // Display total long-term count and newly found count
        let displayHtml: string = `<div>已存储 AI 服务密钥总数: ${totalLongTermKeyCount}</div>`;
        displayHtml += `<div>本次新发现密钥数: ${newlyFoundKeys.length}</div>`;

        UIElements.keysDisplayElement.innerHTML = displayHtml;

        // Keep the status element update for detailed counts
        if (UIElements.statusElement) {
            let statusText: string = `已存储 ${totalLongTermKeyCount} 个 AI 服务密钥 (`;
            const parts: string[] = [];
            AI_SERVICES.forEach((service: { name: string }) => {
                if (categoryCounts[service.name] > 0) {
                    parts.push(`${service.name}: ${categoryCounts[service.name]}`);
                }
            });
            statusText += parts.join(", ") + ")";
            statusText += ` | 本次新发现: ${newlyFoundKeys.length}`;
            UIElements.statusElement.textContent = statusText;
        }
    }

    /**
     * Updates the visibility of panel content elements based on panelContentVisible state.
     * Uses a common CSS class and dataset for initial display style.
     */
    static updatePanelContentVisibility = (): void => {
        const panelContentElements: NodeListOf<Element> = document.querySelectorAll(".panel-content-toggleable");
        panelContentElements.forEach((element) => {
            if (element instanceof HTMLElement) {
                if (UIElements.panelContentVisible) {
                    element.style.display = element.dataset.initialDisplay || "block";
                } else {
                    element.style.display = "none";
                }
            }
        });
    }

    /**
     * Updates the progress display element.
     * @param {string} text - The text to display.
     */
    static updateProgressDisplay = (text: string): void => {
        if (UIElements.progressElement) {
            UIElements.progressElement.textContent = text;
        }
    }
}